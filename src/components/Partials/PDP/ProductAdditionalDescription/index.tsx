"use client";

import React from "react";
import { ProductAdditionalDescriptionType } from "@/types/PDP/ProductAdditionalDescription";

/**
 * ProductAdditionalDescription Component
 * 
 * Renders additional product description content using dangerouslySetInnerHTML
 * for rich HTML content display.
 * 
 * @param data - Additional description data containing HTML content
 */
interface ProductAdditionalDescriptionProps {
  data?: ProductAdditionalDescriptionType;
}

const ProductAdditionalDescription: React.FC<ProductAdditionalDescriptionProps> = ({
  data,
}) => {
  // Early return if no data or description provided
  if (!data?.description) {
    return null;
  }

  return (
    <div
      className="mt-12.5 font-obviously"
      dangerouslySetInnerHTML={{
        __html: data.description,
      }}
    />
  );
};

export default ProductAdditionalDescription;
