"use client";

import React, { useState, useRef, useCallback, useEffect } from "react";
import Img from "@/components/Elements/img";
import {
  Carousel,
  CarouselContent,
  CarouselDots,
  CarouselItem,
  type CarouselApi,
} from "@/components/ui/carousel";
import { ChevronLeft, ChevronRight, X } from "lucide-react";

interface MobileProductCarouselProps {
  images: string[];
  title?: string;
  currentSlide: number;
  onSlideChange: (index: number) => void;
  primaryColor?: string;
}

export default function MobileProductCarousel({
  images,
  title,
  currentSlide,
  onSlideChange,
}: Omit<MobileProductCarouselProps, "primaryColor">) {
  // Note: primaryColor prop is available in the interface but not currently used in mobile carousel
  const [carouselApi, setCarouselApi] = useState<CarouselApi>();
  const [modalOpen, setModalOpen] = useState(false);
  const [modalCurrentSlide, setModalCurrentSlide] = useState(0);

  const modalThumbnailContainerRef = useRef<HTMLDivElement>(null);
  const mainThumbnailContainerRef = useRef<HTMLDivElement>(null);

  // Touch/swipe handling for modal
  const touchStartRef = useRef<{ x: number; y: number; time: number } | null>(
    null
  );
  const modalImageRef = useRef<HTMLDivElement>(null);

  // Auto-scroll thumbnail container to center the active thumbnail (horizontal for mobile modal)
  const scrollThumbnailToCenter = useCallback(
    (index: number, containerRef: React.RefObject<HTMLDivElement | null>) => {
      const container = containerRef.current;
      if (!container) return;

      const thumbnails = container.querySelectorAll("button");
      const targetThumbnail = thumbnails[index];
      if (!targetThumbnail) return;

      // Horizontal scrolling for mobile modal bottom thumbnails
      const containerWidth = container.clientWidth;
      const thumbnailWidth = targetThumbnail.clientWidth;
      const thumbnailLeft = targetThumbnail.offsetLeft;

      // Calculate the scroll position to center the thumbnail horizontally
      const scrollLeft =
        thumbnailLeft - containerWidth / 2 + thumbnailWidth / 2;

      // Ensure scroll position is within bounds
      const maxScrollLeft = container.scrollWidth - containerWidth;
      const clampedScrollLeft = Math.max(
        0,
        Math.min(scrollLeft, maxScrollLeft)
      );

      container.scrollTo({
        left: clampedScrollLeft,
        behavior: "smooth",
      });
    },
    []
  );

  // Handle slide change from main carousel
  const handleSlideChange = useCallback(
    (index: number) => {
      onSlideChange(index);
    },
    [onSlideChange]
  );

  // Handle thumbnail click for main carousel
  const handleMainThumbnailClick = useCallback(
    (index: number) => {
      carouselApi?.scrollTo(index);
      setTimeout(() => {
        scrollThumbnailToCenter(index, mainThumbnailContainerRef);
      }, 50);
    },
    [carouselApi, scrollThumbnailToCenter]
  );

  // Handle modal thumbnail click
  const handleModalThumbnailClick = useCallback(
    (index: number) => {
      setModalCurrentSlide(index);
      setTimeout(() => {
        scrollThumbnailToCenter(index, modalThumbnailContainerRef);
      }, 50);
    },
    [scrollThumbnailToCenter]
  );

  // Handle image click to open modal
  const handleImageClick = useCallback(() => {
    setModalCurrentSlide(currentSlide);
    setModalOpen(true);
  }, [currentSlide]);

  // Modal navigation functions
  const goToPreviousImage = useCallback(() => {
    const newIndex =
      modalCurrentSlide > 0 ? modalCurrentSlide - 1 : images.length - 1;
    setModalCurrentSlide(newIndex);
    setTimeout(() => {
      scrollThumbnailToCenter(newIndex, modalThumbnailContainerRef);
    }, 50);
  }, [modalCurrentSlide, images.length, scrollThumbnailToCenter]);

  const goToNextImage = useCallback(() => {
    const newIndex =
      modalCurrentSlide < images.length - 1 ? modalCurrentSlide + 1 : 0;
    setModalCurrentSlide(newIndex);
    setTimeout(() => {
      scrollThumbnailToCenter(newIndex, modalThumbnailContainerRef);
    }, 50);
  }, [modalCurrentSlide, images.length, scrollThumbnailToCenter]);

  // Touch event handlers for swipe gestures (only for single-touch swipes)
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    // Only handle single-touch events for swipe (ignore multi-touch for pinch-zoom)
    if (e.touches.length !== 1) {
      touchStartRef.current = null;
      return;
    }

    const touch = e.touches[0];
    touchStartRef.current = {
      x: touch.clientX,
      y: touch.clientY,
      time: Date.now(),
    };
  }, []);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    // Prevent default to avoid interference with zoom
    if (touchStartRef.current && e.touches.length === 1) {
      const touch = e.touches[0];
      const deltaX = Math.abs(touch.clientX - touchStartRef.current.x);
      const deltaY = Math.abs(touch.clientY - touchStartRef.current.y);

      // If horizontal movement is greater than vertical, prevent default scrolling
      if (deltaX > deltaY && deltaX > 10) {
        e.preventDefault();
      }
    }
  }, []);

  const handleTouchEnd = useCallback(
    (e: React.TouchEvent) => {
      // Only handle single-touch events for swipe
      if (!touchStartRef.current || e.changedTouches.length !== 1) {
        touchStartRef.current = null;
        return;
      }

      const touch = e.changedTouches[0];
      const deltaX = touch.clientX - touchStartRef.current.x;
      const deltaY = touch.clientY - touchStartRef.current.y;
      const deltaTime = Date.now() - touchStartRef.current.time;

      // Check if it's a valid swipe (horizontal movement > vertical, fast enough, long enough)
      const isHorizontalSwipe =
        Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 30;
      const isQuickSwipe = deltaTime < 800; // Increased time threshold
      const isLongEnoughSwipe = Math.abs(deltaX) > 80; // Increased distance threshold

      if (isHorizontalSwipe && isQuickSwipe && isLongEnoughSwipe) {
        // Prevent any default behavior
        e.preventDefault();

        if (deltaX > 0) {
          // Swipe right - go to previous image
          goToPreviousImage();
        } else {
          // Swipe left - go to next image
          goToNextImage();
        }
      }

      touchStartRef.current = null;
    },
    [goToPreviousImage, goToNextImage]
  );

  // Auto-scroll main thumbnail when currentSlide changes
  useEffect(() => {
    scrollThumbnailToCenter(currentSlide, mainThumbnailContainerRef);
  }, [currentSlide, scrollThumbnailToCenter]);

  // Auto-scroll modal thumbnail when modalCurrentSlide changes
  useEffect(() => {
    if (modalOpen) {
      const timeoutId = setTimeout(() => {
        scrollThumbnailToCenter(modalCurrentSlide, modalThumbnailContainerRef);
      }, 150);
      return () => clearTimeout(timeoutId);
    }
  }, [modalCurrentSlide, modalOpen, scrollThumbnailToCenter]);

  return (
    <>
      {/* Mobile Carousel - Only visible on mobile */}
      <div className="lg:hidden mb-6">
        <div className="w-full relative">
          <Carousel
            className="w-full relative"
            opts={{ loop: true }}
            setApi={setCarouselApi}
            onSlideChange={handleSlideChange}
          >
            <div className="rounded-lg overflow-hidden">
              <CarouselContent>
                {images.map((image, index) => (
                  <CarouselItem key={index}>
                    <div
                      className="relative aspect-square w-full flex items-center justify-center cursor-zoom-in bg-gray-50"
                      onClick={handleImageClick}
                    >
                      <Img
                        src={image}
                        alt={`${title || "Product"} - Image ${index + 1}`}
                        fill
                        className="object-contain h-full w-full"
                      />
                    </div>
                  </CarouselItem>
                ))}
              </CarouselContent>
            </div>

            {/* Dot indicators */}
            <div className="flex justify-center gap-2 mt-3">
              <CarouselDots
                className="gap-2"
                baseClassName="transition-all duration-300"
                activeClassName="bg-[#44426A] w-1.5 h-1.5 rounded-full"
                inactiveClassName="bg-gray-300 w-1.5 h-1.5 rounded-full"
              />
            </div>
          </Carousel>

          {/* Thumbnail navigation */}
          <div className="mt-4 flex justify-center">
            <div
              ref={mainThumbnailContainerRef}
              className="max-w-full overflow-x-auto scrollbar-hide"
            >
              <div className="flex gap-2 min-w-fit px-4">
                {images.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => handleMainThumbnailClick(index)}
                    className={`relative w-12 h-12 flex-shrink-0 rounded-lg overflow-hidden border-2 transition-all duration-300 hover:opacity-80 ${
                      currentSlide === index
                        ? "border-[#44426A] ring-2 ring-[#44426A]/30"
                        : "border-gray-200 hover:border-gray-300"
                    }`}
                  >
                    <Img
                      src={image}
                      alt={`${title || "Product"} - Thumbnail ${index + 1}`}
                      fill
                      className="object-cover"
                    />
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile-Specific Lightbox Modal */}
      {modalOpen && (
        <div
          className="fixed inset-0 z-[9999] bg-black"
          style={{
            touchAction: "pan-y", // Prevent modal zoom, allow vertical scrolling only
          }}
        >
          {/* Close button */}
          <button
            onClick={() => setModalOpen(false)}
            className="absolute right-4 top-4 z-[60] rounded-full bg-black/60 hover:bg-black/80 p-3 shadow-lg transition-all duration-200"
            style={{
              minWidth: "44px",
              minHeight: "44px",
              touchAction: "manipulation", // Prevent zoom on button
            }}
          >
            <X className="h-5 w-5 text-white" />
            <span className="sr-only">Close</span>
          </button>

          {/* Main modal content */}
          <div
            className="flex flex-col h-full"
            style={{
              touchAction: "pan-y", // Prevent modal zoom
            }}
          >
            {/* Image display area - takes most of the screen */}
            <div
              className="flex-1 relative min-h-0"
              onTouchStart={handleTouchStart}
              onTouchMove={handleTouchMove}
              onTouchEnd={handleTouchEnd}
              style={{
                touchAction: "pan-y", // Allow swipe detection but prevent modal zoom
              }}
            >
              <div className="absolute inset-0 flex items-center justify-center p-4">
                {/* Image container with proper centering and ISOLATED pinch-zoom */}
                <div
                  ref={modalImageRef}
                  className="relative w-full h-full flex items-center justify-center overflow-hidden"
                  style={{
                    touchAction: "none", // Disable all touch actions on container
                    WebkitUserSelect: "none",
                    userSelect: "none",
                    isolation: "isolate", // Create new stacking context
                  }}
                >
                  <div
                    className="relative w-full h-full"
                    style={{
                      touchAction: "pinch-zoom", // Enable zoom ONLY on image wrapper
                      overflow: "hidden",
                      transform: "translateZ(0)", // Force hardware acceleration
                      willChange: "transform", // Optimize for transforms
                    }}
                  >
                    <Img
                      src={images[modalCurrentSlide]}
                      alt={`${title || "Product"} - Modal Image ${
                        modalCurrentSlide + 1
                      }`}
                      fill
                      className="object-contain"
                      priority
                      sizes="100vw"
                      style={{
                        touchAction: "pinch-zoom", // Enable zoom on image
                        width: "100%",
                        height: "100%",
                        transformOrigin: "center center", // Zoom from center
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* Navigation arrows - positioned on sides */}
              {images.length > 1 && (
                <>
                  <button
                    onClick={goToPreviousImage}
                    className="absolute left-3 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full flex items-center justify-center transition-all duration-200 hover:scale-105 z-50 shadow-md"
                    style={{
                      minWidth: "44px",
                      minHeight: "44px",
                      width: "44px",
                      height: "44px",
                      touchAction: "manipulation", // Prevent zoom on button
                    }}
                  >
                    <ChevronLeft className="h-5 w-5" />
                  </button>
                  <button
                    onClick={goToNextImage}
                    className="absolute right-3 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full flex items-center justify-center transition-all duration-200 hover:scale-105 z-50 shadow-md"
                    style={{
                      minWidth: "44px",
                      minHeight: "44px",
                      width: "44px",
                      height: "44px",
                      touchAction: "manipulation", // Prevent zoom on button
                    }}
                  >
                    <ChevronRight className="h-5 w-5" />
                  </button>
                </>
              )}

              {/* Slide indicator */}
              {images.length > 1 && (
                <div
                  className="absolute top-16 left-1/2 -translate-x-1/2 bg-black/70 text-white px-4 py-2 rounded-full text-sm z-40 font-medium"
                  style={{
                    touchAction: "none", // Prevent zoom on indicator
                  }}
                >
                  {modalCurrentSlide + 1} / {images.length}
                </div>
              )}
            </div>

            {/* Bottom thumbnail navigation */}
            {images.length > 1 && (
              <div
                className="bg-black/90 border-t border-white/10 safe-area-inset-bottom"
                style={{
                  touchAction: "pan-x", // Allow horizontal scrolling, prevent zoom
                }}
              >
                <div className="p-4">
                  <div
                    ref={modalThumbnailContainerRef}
                    className="overflow-x-auto scrollbar-hide"
                    style={{
                      scrollBehavior: "smooth",
                      WebkitOverflowScrolling: "touch",
                      touchAction: "pan-x", // Allow horizontal scrolling only
                    }}
                  >
                    <div className="flex gap-3 min-w-fit">
                      {images.map((image, index) => (
                        <button
                          key={index}
                          onClick={() => handleModalThumbnailClick(index)}
                          className={`relative flex-shrink-0 rounded-lg overflow-hidden border-2 transition-all duration-300 hover:opacity-80 ${
                            modalCurrentSlide === index
                              ? "border-white ring-2 ring-white/50 opacity-100"
                              : "border-white/30 hover:border-white/60 opacity-70"
                          }`}
                          style={{
                            minWidth: "56px",
                            minHeight: "56px",
                            width: "56px",
                            height: "56px",
                            touchAction: "manipulation", // Prevent zoom on thumbnails
                          }}
                        >
                          <Img
                            src={image}
                            alt={`${title || "Product"} - Modal Thumbnail ${
                              index + 1
                            }`}
                            fill
                            className="object-cover"
                          />
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
}
