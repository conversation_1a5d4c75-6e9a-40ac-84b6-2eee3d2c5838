/* eslint-disable @typescript-eslint/no-unused-vars */
/**
 * Bundle Validation Utilities
 *
 * Utility functions for validating BYOB bundle configurations
 * and calculating bundle totals.
 */

import { ProductDetailsType } from "@/types/Collections/ProductDetails";
import { BundleQuantityValidation } from "../types";

/**
 * Calculate total selected quantity across all bundle variants
 */
export function calculateTotalQuantity(
  selectedQuantities: Record<string, number>
): number {
  return Object.values(selectedQuantities).reduce((sum, qty) => sum + qty, 0);
}

/**
 * Get required pack size from product data
 * This function should be updated based on your actual data structure
 */
export function getRequiredPackSize(
  strapiProduct: ProductDetailsType,
  medusaProduct?: any
): number {
  // Try to get from various possible sources
  // Update these paths based on your actual data structure

  // Option 1: From Strapi product metadata
  if (strapiProduct.metadata?.pack_size) {
    return parseInt(strapiProduct.metadata.pack_size.toString(), 10);
  }

  // Option 2: From Medusa product metadata
  if (medusaProduct?.metadata?.pack_size) {
    return parseInt(medusaProduct.metadata.pack_size.toString(), 10);
  }

  // Option 3: From product description or other fields
  if (strapiProduct.short_description) {
    const packSizeMatch =
      strapiProduct.short_description.match(/(\d+)\s*pack/i);
    if (packSizeMatch) {
      return parseInt(packSizeMatch[1], 10);
    }
  }

  // Default fallback - should be configured per product
  return 30;
}

/**
 * Validate bundle configuration
 */
export function validateBundleConfiguration(
  selectedQuantities: Record<string, number>,
  requiredPackSize: number
): BundleQuantityValidation {
  const currentTotal = calculateTotalQuantity(selectedQuantities);
  const isValid = currentTotal === requiredPackSize;

  let errorMessage: string | undefined;

  if (currentTotal === 0) {
    errorMessage = `Please select items for your bundle (${requiredPackSize} items required)`;
  } else if (currentTotal < requiredPackSize) {
    const remaining = requiredPackSize - currentTotal;
    errorMessage = `Please add ${remaining} more item${
      remaining > 1 ? "s" : ""
    } to complete your bundle`;
  } else if (currentTotal > requiredPackSize) {
    const excess = currentTotal - requiredPackSize;
    errorMessage = `Please remove ${excess} item${
      excess > 1 ? "s" : ""
    } from your bundle`;
  }

  return {
    isValid,
    currentTotal,
    requiredTotal: requiredPackSize,
    errorMessage,
  };
}

/**
 * Check if a bundle variant has a maximum quantity limit
 */
export function getVariantMaxQuantity(
  variant: ProductDetailsType
): number | undefined {
  // Check for max quantity in variant metadata
  if (variant.metadata?.max_quantity) {
    return parseInt(variant.metadata.max_quantity.toString(), 10);
  }

  // Default: no limit (undefined means unlimited)
  return undefined;
}

/**
 * Generate bundle configuration for cart/order
 */
export function generateBundleConfiguration(
  selectedQuantities: Record<string, number>,
  bundleVariants: ProductDetailsType[]
): Array<{
  variantId: string;
  productTitle: string;
  quantity: number;
  systemId: string;
}> {
  return Object.entries(selectedQuantities)
    .filter(([_, quantity]) => quantity > 0)
    .map(([variantId, quantity]) => {
      const variant = bundleVariants.find((v) => v.systemId === variantId);
      return {
        variantId,
        productTitle: variant?.title || "Unknown Product",
        quantity,
        systemId: variant?.systemId || variantId,
      };
    });
}

/**
 * Calculate bundle progress percentage
 */
export function calculateBundleProgress(
  currentTotal: number,
  requiredTotal: number
): number {
  return Math.min(100, (currentTotal / requiredTotal) * 100);
}

/**
 * Type guard to check if product data is BYOB
 */
export function isBYOBProduct(productData: any): boolean {
  return (
    productData &&
    "productType" in productData &&
    productData.productType === "BYOB"
  );
}

/**
 * Get bundle variant by ID
 */
export function getBundleVariantById(
  variantId: string,
  bundleVariants: ProductDetailsType[]
): ProductDetailsType | undefined {
  return bundleVariants.find((variant) => variant.systemId === variantId);
}

/**
 * Format bundle summary for display
 */
export function formatBundleSummary(
  selectedQuantities: Record<string, number>,
  bundleVariants: ProductDetailsType[]
): string {
  const selectedItems = Object.entries(selectedQuantities)
    .filter(([_, quantity]) => quantity > 0)
    .map(([variantId, quantity]) => {
      const variant = getBundleVariantById(variantId, bundleVariants);
      return `${quantity}x ${variant?.title || "Unknown"}`;
    });

  return selectedItems.length > 0
    ? selectedItems.join(", ")
    : "No items selected";
}

/**
 * Check if bundle is ready for cart
 */
export function isBundleReadyForCart(
  selectedQuantities: Record<string, number>,
  requiredPackSize: number
): boolean {
  const validation = validateBundleConfiguration(
    selectedQuantities,
    requiredPackSize
  );
  return validation.isValid;
}

/**
 * Check if plus button should be disabled for a variant
 */
export function shouldDisablePlusButton(
  variantId: string,
  selectedQuantities: Record<string, number>,
  bundleVariants: ProductDetailsType[],
  requiredPackSize: number
): boolean {
  const currentQuantity = selectedQuantities[variantId] || 0;
  const totalQuantity = calculateTotalQuantity(selectedQuantities);
  const variant = bundleVariants.find((v) => v.systemId === variantId);
  const maxQuantity = variant ? getVariantMaxQuantity(variant) : undefined;

  // Disable if total bundle limit reached
  if (totalQuantity >= requiredPackSize) {
    return true;
  }

  // Disable if individual variant max quantity reached
  if (maxQuantity !== undefined && currentQuantity >= maxQuantity) {
    return true;
  }

  return false;
}

/**
 * Check if minus button should be disabled for a variant
 */
export function shouldDisableMinusButton(
  variantId: string,
  selectedQuantities: Record<string, number>
): boolean {
  const currentQuantity = selectedQuantities[variantId] || 0;
  return currentQuantity <= 0;
}

/**
 * Get maximum allowed quantity for a specific variant considering bundle limits
 */
export function getMaxAllowedQuantityForVariant(
  variantId: string,
  selectedQuantities: Record<string, number>,
  bundleVariants: ProductDetailsType[],
  requiredPackSize: number
): number {
  const variant = bundleVariants.find((v) => v.systemId === variantId);
  const variantMaxQuantity = variant
    ? getVariantMaxQuantity(variant)
    : undefined;

  // Calculate how much space is left in the bundle
  const otherVariantsTotal = Object.entries(selectedQuantities)
    .filter(([id]) => id !== variantId)
    .reduce((sum, [, qty]) => sum + qty, 0);

  const bundleSpaceLeft = requiredPackSize - otherVariantsTotal;

  // Return the minimum of variant max and bundle space left
  if (variantMaxQuantity !== undefined) {
    return Math.min(variantMaxQuantity, bundleSpaceLeft);
  }

  return bundleSpaceLeft;
}

/**
 * Get bundle validation status for UI feedback
 */
export function getBundleValidationStatus(
  validation: BundleQuantityValidation
): "empty" | "incomplete" | "complete" | "excess" {
  if (validation.currentTotal === 0) return "empty";
  if (validation.currentTotal < validation.requiredTotal) return "incomplete";
  if (validation.currentTotal > validation.requiredTotal) return "excess";
  return "complete";
}
