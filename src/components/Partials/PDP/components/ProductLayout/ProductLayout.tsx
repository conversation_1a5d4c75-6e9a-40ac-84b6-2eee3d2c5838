"use client";

import React from "react";
import { cn } from "@/libs/utils";
import { ProductLayoutProps } from "../../types";

/**
 * ProductLayout Component
 *
 * Main layout wrapper for the product details page
 * Provides consistent spacing and responsive grid structure
 */
export const ProductLayout: React.FC<ProductLayoutProps> = ({
  children,
  className,
}) => {
  return (
    <>
      <div className={cn("max-w-[1164px] px-6 mx-auto", className)}>
        <div className="pt-10 pb-12">
          <div className="flex flex-wrap">
            <div className="grid grid-cols-12 gap-4 w-full">{children}</div>
          </div>
        </div>
      </div>
    </>
  );
};
