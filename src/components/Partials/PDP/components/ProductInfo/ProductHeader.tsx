import React, { useMemo } from "react";
import { SameDayDelivery } from "@/assets/icons";
import PageBreadcrumb from "@/components/Common/PageBreadcrumb";
import { ProductHeaderProps } from "../../types";

/**
 * ProductHeader Component
 *
 * Displays product breadcrumb, title, and delivery info
 */
export const ProductHeader: React.FC<ProductHeaderProps> = ({
  strapiProduct,
  medusaProduct,
}) => {
  const primaryColor = strapiProduct?.primary_color || "#036A38";

  // Generate breadcrumb items
  const breadcrumbItems = useMemo(
    () => [
      { label: "Home", href: "/" },
      { label: "Products", href: "/products" },
      { label: medusaProduct?.title || "Product" },
    ],
    [medusaProduct?.title]
  );

  return (
    <>
      <PageBreadcrumb className="px-0 mb-2 mt-2.5" items={breadcrumbItems} />
      <h1
        className="text-[40px] leading-12 mb-8 font-semibold font-narrow"
        style={{ color: primaryColor }}
      >
        {medusaProduct?.title}
      </h1>
      <SameDayDelivery color={primaryColor} />
    </>
  );
};
