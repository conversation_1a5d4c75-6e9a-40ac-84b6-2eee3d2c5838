import React, { useState } from "react";
import MobileProductCarousel from "../../MobileProductCarousel";
import { ProductMobileCarouselProps } from "../../types";
import { useProductContext } from "../../context/ProductContext";

/**
 * ProductMobileCarousel Component
 *
 * Wrapper for the mobile product carousel with props-based data
 */
export const ProductMobileCarousel: React.FC<ProductMobileCarouselProps> = ({
  strapiProduct, // eslint-disable-line @typescript-eslint/no-unused-vars
  medusaProduct, // eslint-disable-line @typescript-eslint/no-unused-vars
}) => {
  const [currentSlide, setCurrentSlide] = useState(0);

  // Get combined images from context (calculated based on active variant)
  const { combinedImages } = useProductContext();

  return (
    <MobileProductCarousel
      images={combinedImages}
      title={medusaProduct?.title || "Product"}
      currentSlide={currentSlide}
      onSlideChange={setCurrentSlide}
    />
  );
};
