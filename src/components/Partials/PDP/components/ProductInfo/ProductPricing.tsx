import React from "react";
import LoyaltyCashback from "@/assets/icons/LoyaltyCashback";
import InfoIcon from "@/assets/icons/Info";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { ProductPricingProps } from "../../types";
import { useProductContext } from "../../context/ProductContext";

/**
 * ProductPricing Component
 *
 * Displays product pricing information including current price, original price,
 * discount percentage, and loyalty cashback information
 */
export const ProductPricing: React.FC<ProductPricingProps> = ({
  strapiProduct,
  medusaProduct, // eslint-disable-line @typescript-eslint/no-unused-vars
}) => {
  const primaryColor = strapiProduct?.primary_color || "#036A38";

  // Get pricing data from context (calculated based on active variant)
  const {
    loyaltyPoints,
    discountPercentage,
    formattedCurrentPrice,
    formattedOriginalPrice,
    hasDiscount,
  } = useProductContext();

  const cashbackPercentage = 2; // Default cashback percentage

  return (
    <div className="mb-4">
      <div className="mb-5">
        <div className="flex items-center gap-2">
          <p
            className="pb-2.5 font-[560] text-nowrap font-obviously text-[26px]"
            style={{ color: primaryColor }}
          >
            {formattedCurrentPrice}
          </p>
          {hasDiscount && (
            <>
              <p className="line-through text-[#1a181e] font-normal text-nowrap font-obviously text-base leading-6">
                {formattedOriginalPrice}
              </p>
              <span
                className="flex items-center gap-1 text-[10.5px] font-semibold leading-[11px] font-obviously rounded-[2.6px] p-1"
                style={{
                  color: primaryColor,
                  backgroundColor: `${primaryColor}4d`,
                }}
              >
                ({discountPercentage}% OFF)
              </span>
            </>
          )}
        </div>
        <p className="text-[#464646] font-obviously font-normal text-sm">
          MRP inclusive of all taxes
        </p>
      </div>

      {/* Loyalty Cashback Section */}
      <div className="flex items-center gap-1.5">
        <LoyaltyCashback color={primaryColor} />
        <p
          className="font-obviously font-[550] text-sm pb-[2px]"
          style={{ color: primaryColor }}
        >
          Earn {cashbackPercentage}% cashback on this purchase
        </p>
        <Tooltip>
          <TooltipTrigger>
            <InfoIcon color={primaryColor} />
          </TooltipTrigger>
          <TooltipContent className="text-white text-[8px] font-[540] leading-3">
            Earn {loyaltyPoints} Truth points on this order. 1 point = 1 rupee
          </TooltipContent>
        </Tooltip>
      </div>
    </div>
  );
};
