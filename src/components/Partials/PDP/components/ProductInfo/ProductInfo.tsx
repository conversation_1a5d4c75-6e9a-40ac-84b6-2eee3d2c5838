"use client";

import React from "react";
import { cn } from "@/libs/utils";
import { ProductInfoProps } from "../../types";
import { ProductHeader } from "./ProductHeader";
import { ProductPricing } from "./ProductPricing";
import { ProductActions } from "./ProductActions";
import { ProductMobileCarousel } from "./ProductMobileCarousel";

/**
 * ProductInfo Component
 *
 * Main product information section containing:
 * - Product header (breadcrumb, title, delivery info)
 * - Mobile product carousel
 * - Product pricing
 * - Product actions (variants, quantity, cart)
 */
export const ProductInfo: React.FC<ProductInfoProps> = ({
  className,
  strapiProduct,
  medusaProduct,
  onAddToCart,
  onCouponClick,
  productType = "VARIANT",
  bundleVariants = [],
}) => {
  return (
    <div className={cn("col-span-12 lg:col-span-5 lg:pr-12", className)}>
      {/* Product Header */}
      <ProductHeader
        strapiProduct={strapiProduct}
        medusaProduct={medusaProduct}
      />

      {/* Mobile Product Carousel */}
      <ProductMobileCarousel
        strapiProduct={strapiProduct}
        medusaProduct={medusaProduct}
      />

      {/* Product Pricing */}
      <ProductPricing
        strapiProduct={strapiProduct}
        medusaProduct={medusaProduct}
      />

      {/* Product Actions */}
      <ProductActions
        strapiProduct={strapiProduct}
        medusaProduct={medusaProduct}
        onAddToCart={onAddToCart}
        onCouponClick={onCouponClick}
        productType={productType}
        bundleVariants={bundleVariants}
      />
    </div>
  );
};
